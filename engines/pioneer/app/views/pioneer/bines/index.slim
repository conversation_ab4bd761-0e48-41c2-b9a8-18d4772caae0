= content_for :title do
  | Pioneer - Bines


.panel.panel-default
  .panel-body
    .row
      .col-sm-12
        h3 = t('pioneer.bines.title')
    .row.mb-5
      .col-sm-2
        strong = t('pioneer.bines.filte-store') + ":"
      .col-sm-3
        select id= "go-to-store" class="form-control"
          option value=""
          - Mkp::Store.find_each do |store|
            - if current_admin.is_store_owner?(store)
              option value="#{store.id}" selected=(params[:store_id].to_i == store.id) = store.name.titleize
      .col-sm-5
      .col-sm-2
        - if can?(:crud, 'Bines')
          - if params[:brand] == 'modo'
            = link_to( content_tag(:i, '', class: ['fa', 'fa-plus', 'pr-3']) + t('pioneer.bines.create'), new_bin_url(brand: 'modo'), class: 'btn btn-success' )
          - elsif params[:brand] == 'loan_bna'
            = link_to( content_tag(:i, '', class: ['fa', 'fa-plus', 'pr-3']) + t('pioneer.bines.create'), new_bin_url(brand: 'loan_bna'), class: 'btn btn-success' )
          - else
            = link_to( content_tag(:i, '', class: ['fa', 'fa-plus', 'pr-3']) + t('pioneer.bines.create'), new_bin_url, class: 'btn btn-success' )
    .row
      .col-sm-6
        = page_entries_info(@bines, model: 'Bines')
      .col-sm-6.pagination
        = will_paginate(@bines, class: 'right')
        br
        br

    .row
      .col-xs-12
        table.table
          thead
            th scope="col" = t('pioneer.bines.store')
            th scope="col" = t('pioneer.bines.bank')
            th scope="col" = t('pioneer.bines.number')
            th scope="col" = t('pioneer.bines.installments')
            th scope="col" = t('pioneer.bines.from')
            th scope="col" = t('pioneer.bines.to')
            th scope="col"

          - @bines.each do |bin|
            tr data-id= '#{bin.id}'
              td
                img src= "#{bin.store.present? ? bin.store.logo : '/assets/logos/logo-full.png'}" style="height: 25px;"
              td
                = bin.bank&.name
              td
                = bin.number
              td
                - installments_availables = bin.installments.uniq.sort_by(&:number).collect(&:full_description)
                - installments_availables.each do |installment|
                  span.ml-3 #{installment}
              td
                = bin.starts_at.to_formatted_s(:rfc822)
              td
                = bin.ends_at.to_formatted_s(:rfc822)
              td
                - if can?(:crud, 'Bines')
                  = link_to(content_tag(:i, '', class: ['fa', 'fa-edit', 'px-2']), edit_bin_path(bin), class: 'px-2', title:'Edit')
                  = link_to(content_tag(:i, '', class: ['fa', 'fa-trash', 'px-2']), bin_path(bin), action: :destroy, confirm: 'Are you sure?', method: :delete, class: 'px-2', title:'Delete')
    .row
      .col-sm-12.pagination
        = will_paginate(@bines, class: 'right')

- content_for :js do
  javascript:
    $('#go-to-store').change(function () {
      var val = $(this).val();
      window.location.href = "/admin/pioneer/bines?store_id=" + val;
    });
