- installments = (1..12).to_a | [15,18,24,36,50]
- amountInstallments = installments.size #
- amountColumns = 3 #remember modify .col-md-4 as well
- installmentsPerColumn = (amountInstallments.to_f/amountColumns.to_f).ceil
- columsInstallments = installments.each_slice(installmentsPerColumn).to_a
- banks = ["Banco Ciudad","Banco Credicoop","Banco Galicia","Banco Hipotecario","Banco Itaú","Banco Provincia de Buenos Aires","Banco Frances","Banco Macro","Banco Patagonia","Citibank" ,"HSBC","ICBC", "Sin Banco"].sort
- stores = current_user.has_role?(:administrator) ? Mkp::Store.all : [current_user.role.store]

.panel.panel-default
  .panel-body
    = form_for @bin do |f|
      .row
        .col-lg-6.form-group
          = f.label :number, t('pioneer.bines.number')
          = f.text_field :number, required: true, autofocus: true, placeholder: '', title: 'The code can only contain letters and numbers', class: 'form-control', label: false
        .col-lg-3.form-group
          = f.label :number, t('pioneer.bines.from')
          = f.text_field :starts_at, required: true, class: 'form-control', label: false
        .col-lg-3.form-group
          = f.label :number, t('pioneer.bines.to')
          = f.text_field :ends_at, required: true, class: 'form-control', label: false
      hr
      .row
        .col-lg-6
          .col-lg-4
            = f.label :bank_id, t('pioneer.bines.bank')
          .col-lg-8.form-group
            = f.select :bank_id, options_for_select(Mkp::Bank.where(is_issuer: true).pluck(:name, :id).sort, @bin.bank_id), {include_blank: true, label: false}, {class: 'form-control'}
        .col-lg-6
          .col-lg-4
            = f.label :brand, t('pioneer.bines.brand')
          .col-lg-8
            - if params[:brand] == 'modo' || params[:brand] == 'loan_bna'
              = f.hidden_field :brand, value: @bin.brand
            = f.select :brand, options_for_select(Bin.brands.keys, @bin.brand), {include_blank: true, label: false}, {class: 'form-control', disabled: (params[:brand] == 'modo' || params[:brand] == 'loan_bna')}
      hr
      .row
        .col-lg-6
          - if params[:brand] != 'modo' && params[:brand] != 'loan_bna'
            .col-md-4
              = f.label :store_id, t('pioneer.bines.store'), label: false
            .col-md-8
              = f.select :store_id, options_for_select(stores.map { |s| [s.name.capitalize, s.id.to_s]}, @bin.store_id), {include_blank: true, label: false}, {class: 'form-control'}

      - if (@bin.modo? || @bin.loan_bna?) && @associated_shops.present?
        .col-lg-12
          - if @bin.modo?
            label= t('modo_bines.associated_shops')
          - elsif @bin.loan_bna?
            label= t('loan_bna_bines.associated_shops')
          ul
          - @associated_shops.each do |shop_store|
            li.link = link_to(shop_store.shop.name, edit_shop_path(shop_store.shop))
      br
      .row
        .col-lg-12.mt-4
          = f.label :installments, t('pioneer.bines.installments-available'), label: false
      br
      .row
        .col-lg-12
          = f.fields_for :installments do |installments_form|
            = render "installment_fields", f: installments_form
          .links
          = link_to_add_association content_tag(:i, '', class: ['fa', 'fa-plus', 'pr-3']) + t('pioneer.bines.installments-create'), f, :installments, class: 'btn btn-success'
      hr
      .col-lg-5
      button.btn.btn-danger.col-lg-2 type= 'submit' style= 'aligne: center'
        = @bin.persisted? ? t('pioneer.bines.update') : t('pioneer.bines.create')
      .col-lg-5

- content_for(:js) do
  javascript:
    $(document).ready(function(){
      $('#bin_starts_at').datetimepicker();
      $('#bin_ends_at').datetimepicker();
    });

    function filter(id) {
      var keyword = document.getElementById(id).value;
      var select = document.getElementById(id.replace('_search', '_category_id'));
      for (var i = 0; i < select.length; i++) {
        var txt = select.options[i].text;

          if (txt.toLowerCase().includes(keyword.toLowerCase()) && keyword.trim() !== "") {
              $(select.options[i]).removeAttr('disabled').show();
          } else {
              $(select.options[i]).attr('disabled', 'disabled').hide();
          }
      }
    };

    function filter_products(id) {
        var id_value_string = document.getElementById(id).value;
        if (id_value_string == "") {
            select = document.getElementById(id.replace('_category_id', '_product_id'));
            var length = select.options.length;
            for (i = 0; i < length; i++) {
                select.remove(0);
            }
        } else {
            // Send the request and update course dropdown
            $.ajax({
                dataType: "json",
                cache: false,
                url: '/admin/pioneer/get_products_by_category/' + id_value_string,
                error: function (XMLHttpRequest, errorTextStatus, error) {
                    alert("Failed finding products : " + errorTextStatus + " ;" + error);
                },
                success: function (data) {
                    // Clear all options from course select
                    select = document.getElementById(id.replace('_category_id', '_product_id'));
                    var length = select.options.length;
                    for (i = 0; i < length; i++) {
                        select.remove(0);
                    }
                    //put in a empty default line
                    var opt = document.createElement('option');
                    opt.value = '';
                    opt.innerHTML = 'Select a product...';
                    select.appendChild(opt);
                    // Fill course select
                    for (var i = 0; i < data.length; i++) {
                        var opt = document.createElement('option');
                        opt.value = data[i].id;
                        opt.innerHTML = data[i].title;
                        select.appendChild(opt);
                    }
                }
            })
        }
    };
