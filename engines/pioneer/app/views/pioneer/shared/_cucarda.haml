.py-4.mt-4.border-top
  %h4= t('pioneer.cucardas.config')
  .row.my-4
    .col-xs-6
      = form.collection_select :cucarda_id,
                         [[t('pioneer.cucardas.select'), nil]] + Cucarda.all.map { |c| [c.name, c.id] },
                         :last,
                         :first,
                         { include_blank: false, selected: nil, label: t('pioneer.cucardas.title') },
                         { class: "form-control", id: "mkp_category_cucarda_id" } 

    .col-xs-6
      - store_id = params[:store_id]
      - if store_id.present?
        - store_name = Mkp::Store.find_by(id: store_id)&.name
        .checkbox
          = form.label :cucarda_active do
            = form.check_box :cucarda_active,
              class: "cucarda-active-toggle",
              data: { url: update_cucarda_stores_categories_path },
              checked: @category&.store_names&.include?(store_name)  
      - else
        .checkbox
          = form.label :cucarda_active do
            = form.check_box :cucarda_active, class: "cucarda-active-toggle", data: { url: update_cucarda_stores_categories_path }

:javascript
  document.addEventListener('DOMContentLoaded', function() {
    $(document).on('change', '.cucarda-active-toggle', function() {
      var cucardaActive = this.checked;
      var cucardaId = $('#mkp_category_cucarda_id').val(); 
      var categoryId = window.location.pathname.split('/').filter(function(part) {
        return part.match(/^\d+$/);
      }).pop();
      var url = $(this).data('url');
      var storeId = new URLSearchParams(window.location.search).get('store_id'); 
      $.ajax({
        type: 'PATCH',
        url: url,
        data: {
          id: categoryId,
          mkp_category: {
            cucarda_id: cucardaId,
            cucarda_active: cucardaActive
          },
          store_id: storeId 
        }
        
      });
    });
  });


