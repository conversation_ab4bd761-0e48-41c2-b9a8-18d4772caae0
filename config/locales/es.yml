# Sample localization file for Spanish. Add more files in this directory for other locales.
# See https://github.com/svenfuchs/rails-i18n/tree/master/rails%2Flocale for starting points.

es:
  will_paginate:
    previous_label: «
    next_label: »
    simple_previous_label: « Anterior
    simple_next_label: Próxima »
    page_gap: "&hellip;"
  date:
    abbr_day_names:
    - Dom
    - Lun
    - Mar
    - Mi<PERSON>
    - Jue
    - Vie
    - Sáb
    abbr_month_names:
    -
    - Ene
    - Feb
    - Mar
    - Abr
    - May
    - Jun
    - Jul
    - Ago
    - Sep
    - Oct
    - Nov
    - Dic
    day_names:
    - Domingo
    - Lunes
    - Martes
    - Miércoles
    - Jueves
    - Viernes
    - Sábado
    formats:
      default: ! '%d-%m-%Y'
      long: ! '%d de %B de %Y'
      short: ! '%d de %b'
      day_and_year: ! "%B %d, %Y"
      month_and_year: ! "%B %Y"
    month_names:
    -
    - Enero
    - Febrero
    - Marzo
    - Abril
    - Mayo
    - Junio
    - Julio
    - Agosto
    - Septiembre
    - Octubre
    - Noviembre
    - Diciembre
    order:
    - :day
    - :month
    - :year
  datetime:
    distance_in_words:
      about_x_hours:
        one: hace 1 hora
        other: hace %{count} horas
      about_x_months:
        one: hace 1 mes
        other: hace %{count} meses
      about_x_years:
        one: hace 1 año
        other: hace %{count} años
      almost_x_years:
        one: hace 1 año
        other: hace %{count} años
      half_a_minute: hace medio minuto
      less_than_x_minutes:
        one: hace menos de 1 minuto
        other: hace menos de %{count} minutos
      less_than_x_seconds:
        one: hace menos de 1 segundo
        other: hace menos de %{count} segundos
      over_x_years:
        one: hace más de 1 año
        other: hace más de %{count} años
      x_days:
        one: hace 1 día
        other: hace %{count} días
      x_minutes:
        one: hace 1 minuto
        other: hace %{count} minutos
      x_months:
        one: hace 1 mes
        other: hace %{count} meses
      x_seconds:
        one: hace 1 segundo
        other: hace %{count} segundos
    prompts:
      day: Día
      hour: Hora
      minute: Minuto
      month: Mes
      second: Segundos
      year: Año
  errors: &errors
    format: ! '%{attribute} %{message}'
    messages:
      accepted: debe ser aceptado
      blank: no puede estar en blanco
      confirmation: no coincide con la confirmación
      empty: no puede estar vacío
      equal_to: debe ser igual a %{count}
      even: debe ser un número par
      exclusion: está reservado
      greater_than: debe ser mayor que %{count}
      greater_than_or_equal_to: debe ser mayor o igual que %{count}
      inclusion: no está incluído en la lista
      invalid: es inválido
      less_than: debe ser menor que %{count}
      less_than_or_equal_to: debe ser menor o igual que %{count}
      not_a_number: no es un número
      not_an_integer: debe ser un entero
      odd: debe ser un número non
      record_invalid: ! 'La validación falló: %{errors}'
      taken: ya ha sido tomado
      too_long:
        one: es demasiado largo (máximo 1 caracter)
        other: es demasiado largo (máximo %{count} caracteres)
      too_short:
        one: es demasiado corto (mínimo 1 caracter)
        other: es demasiado corto (mínimo %{count} caracteres)
      wrong_length:
        one: longitud errónea (debe ser de 1 caracter)
        other: longitud errónea (debe ser de %{count} caracteres)
    template:
      body: ! 'Revise que los siguientes campos sean válidos:'
      header:
        one: ! '%{model} no pudo guardarse debido a 1 error'
        other: ! '%{model} no pudo guardarse debido a %{count} errores'
  helpers:
    select:
      prompt: Por favor selecciona
    submit:
      create: Crear %{model}
      submit: Guardar %{model}
      update: Actualizar %{model}
  number:
    currency:
      format:
        delimiter: '.'
        format: ! '%u%n'
        precision: 2
        separator: ','
        significant: false
        strip_insignificant_zeros: false
        unit: $
    format:
      delimiter: '.'
      precision: 2
      separator: ','
      significant: false
      strip_insignificant_zeros: false
    human:
      decimal_units:
        format: ! '%n %u'
        units:
          billion: Mil millones
          million: Millón
          quadrillion: Mil billones
          thousand: Mil
          trillion: Billón
          unit: ''
      format:
        delimiter: ! '.'
        precision: 2
        significant: true
        strip_insignificant_zeros: false
      storage_units:
        format: ! '%n %u'
        units:
          byte:
            one: Byte
            other: Bytes
          gb: GB
          kb: KB
          mb: MB
          tb: TB
    percentage:
      format:
        delimiter: ! '.'
    precision:
      format:
        delimiter: ! '.'
  support:
    array:
      last_word_connector: ! ' y '
      two_words_connector: ! ' y '
      words_connector: ! ', '
  time:
    am: am
    formats:
      default: ! '%a, %d de %b de %Y a las %H:%M:%S %Z'
      long: ! '%A, %d de %B de %Y a las %I:%M %p'
      short: ! '%d de %b del %Y a las %H:%M hs.'
      shorter: ! '%d de %B, %H:%M:%S hs.'
      shortest: ! '%d de %B del %Y'
    pm: pm

  # remove these aliases after 'activemodel' and 'activerecord' namespaces are removed from Rails repository
  activemodel:
    errors:
      <<: *errors
  activerecord:
    errors:
      <<: *errors
      models:
        user:
          attributes:
            current_password:
              invalid: Does not match your current password.
        mkp_shop:
          attributes:
            decidir_site_id: ID SITIO
    attributes:
      mkp/customer:
        doc_number: Número de documento
      mkp/shop:
        decidir_site_id: ID SITIO
      gateway_alternative_strategy:
        strategy: Estrategia
  controllers:
    application:
      login_required: Primero debe loguearse para acceder a esta pagina.
      logout_required: Primero debe desloguearse para acceder a esta pagina.
    password_resets:
      user_not_found: No existe usuario con ese Email.
      password_updated: Contraseña actualizada con exito.
      token_not_found: |
                       We're sorry, but we could not locate your account.
                       If you are having issues, try copying and pasting the URL
                       from your email into your browser or restarting the
                       reset password process.
    authentications:
      auth_taken: No podés conceder acceso a esa cuenta de Facebook porque está asociada a otro usuario de Avenida.com
  error_pages:
    go_to_home: "Ir a la home"
    error_404:
      title: "¡Ups! nada por aquí"
      description: "Puedes realizar una nueva búsqueda o conocer alguno de los siguientes productos"
    error_422:
      title: "Ha ocurrido un error"
      description: "Hemos sido notificados del problema y lo arreglaremos a la brevedad"
    error_500:
      title: "Ha ocurrido un error"
      description: "Hemos sido notificados del problema y lo arreglaremos a la brevedad"
  notifications:
    commented_post_html: "%{actor} comentó en tu %{post_type}."
    followed_html: "%{actor} te está siguiendo."
    commented_same_post_html: "%{actor} también comento en el %{post_type} de %{author}."
    answered_html: "%{actor} respondió a tu pregunta sobre el producto %{product}. Te enviamos un email con la respuesta. (¡No te olvides chequear tu carpeta de Spam!)"
    favorited_html: "%{actor} hizo high five en tu %{post_type}."
    mentioned_html: "%{actor} te mencionó."
  notification_mailer:
    mentioned:
      subject: "@%{login} te mencionó en un %{item_class_name}"
      comment: comentario
      post: post
      main_text_html: "te mencionó en un %{item_class_name}:"
      see_text_html: "Ver %{item_class_name}"
    commented_post:
      subject: "@%{login} comentó en tú post: %{post_title}"
      title: comentó en tu post
    commented_same_post:
      subject: "@%{login} comentó en el post de @%{user_login}: %{post_title}"
      title: "comentó en el post de @%{user_login}"
    favorited:
      subject: "@%{login} acaba de hacer High5 en tu post: %{title}"
      title: acaba de hacer High5 en tu post
    followed:
      subject: "@%{login} te está siguiendo"
      back_subject: "%{full_name} (@%{login}) también te está siguiendo"
      title: te está siguiendo
      followed_by: Seguido por
      followed_and: y
      followed_others:
        one: otro
        other: "otros %{count}"
      following: "Siguiendo:"
      followers: "Seguidores:"
      message_title: Tenés un nuevo seguidor en Avenida.com!
    see_post: Ver Post
    see_comment_thread: Ver Historial de Comentarios
    see_profile: Ver Perfil
    follow: Seguir
    following: Siguiendo
  models:
    mkp:
      customer:
        bna_registration_error: El email o DNI ya se encuentra registrado. Si considera esto un error, comunicate al 810 4444 500 de lunes a sábados de 08 a 20 hs.
        invalid_username_password: El usuario o contraseña son incorrectos
        registration_required: Es necesario que actualices tus datos. Por favor haz clic en “Registrarme” para actualizar los mismos.
  password_resets:
    new:
      title: Restablecer Contraseña
      description: Completá con tu dirección de correo y recibirás las instrucciones para obtener tu nueva contraseña.
      email: Email
      reset: Restablecer mi Contraseña
      placeholder: Ingresá login ó email
    edit:
      title: Contraseña Nueva
      description: Por favor, ingresá tu contraseña nueva.
      new_password: Contraseña Nueva
      confirm_password: Verificar Contraseña
      update_password: Actualizar mi Contraseña
    done:
      title: Éxito!
      description: Las instrucciones para restablecer tu contraseña serán enviadas a tu casilla de correo electrónico.
  promotion_mailer:
    entered_contest:
      subject: Te Sumamos a un Concurso
      hey: Hola @%{login},
      we_miss_you_html: Te extrañamos y queriamos estar seguros de que no te perdes ninguna oportunidad de ganar buenos premios de nuestros amigos de %{href}. Te sumamos a un concurso "Siguiendo" esta marca por vos. Podes ver más de su contenido y productos en su perfil en Avenida.com.com.
      name_href: '%{name}'
      follow_other_brands: Seguí otras marcas y personas para ver que pasa en Avenida.com!
      go_to: Ir a Avenida.com
  integration_mailer:
    import_and_sync:
      subject: Productos Importados de %{integration_name}
      body:
        zero: No se importó ningún producto nuevo.
        one: Importado %{count} producto exitosamente.
        other: Importados %{count} productos exitosamente.
      images_message: A partir de ahora puede ver sus productos en el Shop Admin, es posible que las imagenes tarden en aparecer ya que son cargadas de forma asíncrona.
  import_label_mailer:
    notify_errors:
      subject: Errores de Importacion
      body:
        errors: Se han detectado errores en las siguentes subordenes %{row_errors}
    notify_success:
      subject: Importacion Exitosa
      body:
        success: Operacion realizada exitosamente.
  search:
    search:
      social_activity: ACTIVIDAD SOCIAL
      saying_about: "DESCUBRÍ LO QUE OTRAS PERSONAS ESTAN DICIENDO DE %{query}"
      no_feeds_found: No registramos actividad
      users_found: USUARIOS ENCONTRADOS
      products_found: PRODUCTOS ENCONTRADOS
      found_matches: "SE ENCONTRARON %{query}"
      found_matches_more: "SE ENCONTRARON MAS DE %{query}"
      no_users_found: No registramos usuarios
      variants_button: Ver todos los productos encontrados
  user_mailer:
    welcome_email:
      were_stoked: Estamos felices de que te hayas sumado a Avenida.com!
      you_now_have_access: |
                           Ahora vas a tener acceso a lo último en noticias y novedades de las mejores marcas de deportes extremos.
                           Estás empezando a ser parte de una comunidad global que se divierte empujando sus límites y devolviéndole algo al ambiente.
      to_get_the_most: 'Para sacarle el máximo provecho, asegurate de:'
      update_your_profile: Actualizar tu perfil
      follow_brands: Seguir marcas
      follow_people: Seguir a otras personas
      upload_share_video: Subir y compartir videos
      upload_share_photos: Subir y compartir fotos
      keep_livin_the_goodlife: Seguí disfrutando la GoodLife!
      the_goodpeople_crew: El Equipo Avenida.com

  # IMPORTANT Add translations according to the app/views directory
  task:
    feed:
      channel:
        avenida:
          title: Avenida Compras
          description: Encontrá productos para el hogar, electrodomésticos, electro y computación, con ofertas atractivas
          link: https://www.avenida.com.ar
        tiendabuho:
          title: Tienda Buho
          description: Los mejores productos con descuento y financiación, exclusivo para clientes del Banco Hipotecario
          link: https://tiendabuho.com
        tienda_la_nueva:
          title: Tienda La Nueva
          description: TiendaLaNueva.com | Más simple. Más cerca
          link: https://www.tiendalanueva.com/

  promotions:
    amex: AMEX
    cabal: CABAL
    cabal_debito: CABAL DEBITO
    credimas: CREDIMAS
    diners: DINERS
    maestro: MAESTRO
    mastercard: MASTERCARD
    naranja: NARANJA
    visa: VISA
    visa_debito: VISA DEBITO
    visa_recargable: VISA RECARGABLE
    mastercard_debito: MASTERCARD DEBITO
    nativa: NATIVA
    modo_bna: MODO BNA+
    modo_ciudad: MODO CIUDAD
    modo: MODO
    loan_bna: PRÉSTAMOS BNA
  payment-gateways:
    DecidirDistributed: "Decidir distribuido"
    Decidir: "Decidir"
    AvenidaDecidirDistributed: "Decidir distribuido*"
    AvenidaDecidir: "Decidir*"
    AvenidaModoDistributed: "Modo distribuido"
    AvenidaMercadopago: "MercadoPago"
    LoyaltyBna: "Puntos BNA"
    FirstData: "First Data"
    AvenidaModo: "Modo"
  modo_bines:
    associated_shops: Shops asociados
  loan_bna_bines:
    associated_shops: Shops asociados
