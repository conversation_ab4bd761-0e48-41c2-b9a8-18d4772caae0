module Mkp
  module Shipping
    class DeliveryProcessor

      ESTIMATED_DELIVERY_TIMES = {
        three_to_five_days: [
          'Capital Federal',
          'Buenos Aires'
        ],
        four_to_seven_days: [
          'Catamarca',
          'Chaco',
          '<PERSON><PERSON>',
          'Córdoba',
          'Corrientes',
          'Entre Ríos',
          'Formosa',
          'Ju<PERSON><PERSON>',
          'La Pampa',
          'La Rioja',
          'Mendoza',
          'Misiones',
          'Neuquén',
          'Río Negro',
          'Salta',
          'San Juan',
          'San Luis',
          'Santa Cruz',
          'Santa Fe',
          'Santiago del Estero',
          'Tierra del Fuego',
          'Tucumán'
        ]
      }.freeze

      BONIFICATED_CATEGORIES = [ 840, 748, 882, 903, 905, 1328, 1337 ]

      #ids = [469, 479, 494, 499, 495, 1298, 500, 1297]

      attr_reader :checkout_cart, :destination

      def initialize(destination, checkout_cart)
        @checkout_cart = checkout_cart
        @destination = destination
      end

      def find_delivery_options
        store = checkout_cart.store
        shipments_to_deliver = checkout_cart.shipments
        return get_rates_for_points if has_points_products?(shipments_to_deliver)
        return get_rates_for_chequeregalo if checkout_cart.store_id == 7
        # This is only to free shipping.
        # return get_rates_for_shipments_monday if bonification_cybermonday(shipments_to_deliver) && checkout_cart.store_id == 1 && checkout_cart.total > 1499 && ( @destination.state == 'Capital Federal' || @destination.state == 'GBA' )
        # return get_rates_for_cybermonday if cyber_tiendalanueva?(shipments_to_deliver) || cyber_buho?(shipments_to_deliver)
        return get_rates_for_cybermonday if cyber_tiendalanueva?(shipments_to_deliver)
        return get_rates_for_avenida if free_shipping_avenida?(shipments_to_deliver)
        weights = get_order_weights(shipments_to_deliver)
        carrier = store.get_delivery_option(@destination.zip, weights, shipments_to_deliver.keys.map(&:id))
        results = get_rates_for_shipments(carrier)
        results
      end

      def bonification_cybermonday(shipments)
        begin
          shipments.each do |v|
            category = v.last[0][:variant].product.category
            ids_categories = category.sibling_ids << category.id
            return false if ids_categories.any?{ |id| BONIFICATED_CATEGORIES.include? id }
          end
          true
        end
        rescue
          true
      end

      def group_delivery_options(option_tables)
        delivery_options = []
        option_tables.each do |hash, index|
          delivery_option =  hash.options[:shipping]

          result = {
            delivery_speed: delivery_option.service_level,
            carrier: delivery_option.carrier,
            delivery_option: delivery_option
          }

          original_price = delivery_option.charge.to_f
          shipment_cost = if checkout_cart.has_shipment_bonification?
            { price: 0.0, bonificated_price: original_price }
          elsif checkout_cart.has_shipment_promotion?
            delta = delivery_option.charge.to_f / 100.00 * checkout_cart.get_promotion_instance.get_shipping_promotion[:discount].to_f
            price = original_price - delta
            {
              price: price,
              bonificated_price: original_price,
              display_name: checkout_cart.get_promotion_instance.get_shipping_promotion[:display_name]
            }
          else
            { price: original_price }
          end

          delivery_options << result.merge(shipment_cost)
        end

        if has_points_products?(checkout_cart.shipments)
          return { points: delivery_options }
        end

        delivery_options.sort_by { |hsh| hsh[:price] }
      end

      def get_order_weights(shipments)
        weights = shipments.map do |k, v|
          v.inject(0) do |result, element|
            weight = element[:variant].product.weight_with_unit.to.kilograms.value * element[:quantity]
            weight = 2 unless weight > 0.0
            result + weight
          end
        end
      end

      private

      def cyber_tiendalanueva?(shipments_to_deliver)
        from, to = DateTime.new(2018,11,23).beginning_of_day, DateTime.new(2018,12,15).end_of_day
        checkout_cart.store_id == 12 && DateTime.now.between?(from, to) && checkout_cart.total > 2500
      end

      def free_shipping_avenida?(shipments_to_deliver)
        from, to = DateTime.new(2018,12,14).beginning_of_day, DateTime.new(2018,12,16).end_of_day
        checkout_cart.store_id == 1 && DateTime.now.between?(from, to) && checkout_cart.total > 2000
      end

      def has_points_products?(shipments_to_deliver)
        shipments_to_deliver.any? do |_, items|
          items.any? { |item| item[:variant].product.transaction_type == 'points' }
        end
      end

      def cyber_buho?(shipments_to_deliver)
        checkout_cart.store_id == 2 && bonification_cybermonday(shipments_to_deliver) && checkout_cart.total > 1999
      end

      def generate_delivery_options_title(state)
        key = ESTIMATED_DELIVERY_TIMES.select do |_, values|
          values.include?(state)
        end.keys.first || :four_to_seven_days

        I18n.t("mkp.checkout.v5.partials.shipping_methods.estimated_delivery_times.#{key}", locale: :es)
      end

      def get_rates_for_shipments(carrier)
        delivery_option = build_delivery_options(carrier)
        [ DeliveryOptionsTable.new({ shipping: delivery_option }) ]
      end

      def build_delivery_options(carrier)
        Mkp::Shipping::DeliveryOption::Shipping.new(
          charge: carrier[:price],
          title: carrier[:message] || 'Entrega estandar',
          service_level: 'Matriz de Costos',
          carrier: carrier[:carrier],
          service_level_real: carrier[:message],
          external_shipment_id: nil,
          external_rate_id: nil
        )
      end

      def get_rates_for_chequeregalo
        delivery_option = build_delivery_options_chequeregalo
        [ DeliveryOptionsTable.new({ shipping: delivery_option }) ]
      end

      def get_rates_for_cybermonday
        [ DeliveryOptionsTable.new({ shipping: build_delivery_options_monday }) ]
      end

      def get_rates_for_avenida
        [ DeliveryOptionsTable.new({ shipping: build_delivery_options_monday }) ]
      end

      def get_rates_for_points
        [ DeliveryOptionsTable.new({ shipping: build_delivery_options_points }) ]
      end

      def build_delivery_options_monday
        Mkp::Shipping::DeliveryOption::Shipping.new(
          charge: 0,
          title: 'Tu envio está bonificado',
          service_level: 'Avenida',
          carrier: '',
          service_level_real: 'Tu envio está bonificado',
          external_shipment_id: nil,
          external_rate_id: nil
        )
      end

      def build_delivery_options_chequeregalo
        title= "Retiro personalmente por #{@destination.address} #{@destination.street_number}, #{@destination.city}"
        Mkp::Shipping::DeliveryOption::Shipping.new(
          charge: 0,
          title: title,
          service_level: 'Chequeregalo',
          carrier: 'Chequeregalo',
          service_level_real: title,
          external_shipment_id: nil,
          external_rate_id: nil
        )
      end

      def build_delivery_options_points
        Mkp::Shipping::DeliveryOption::Shipping.new(
          charge: 0,
          title: 'Envío gratuito - Productos con puntos',
          service_level: 'Puntos',
          carrier: 'Envío gratuito',
          service_level_real: 'Envío gratuito para productos con puntos',
          external_shipment_id: nil,
          external_rate_id: nil
        )
      end


      class DeliveryOptionsTable
        attr_reader :options

        def initialize(options)
          @options = options
        end

        def has_options?
          options[:shipping].any?
        end
      end
    end
  end
end
