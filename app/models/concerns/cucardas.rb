module Concerns
  module <PERSON><PERSON><PERSON><PERSON>
    extend ActiveSupport::Concern

    included do
      def update_cucarda_id?
        attribute_changed?('store_names') || attribute_changed?('cucarda_id') || attribute_changed?('cucarda_active')
      end

      def cucarda_active?
        cucarda_active
      end

      def update_cucarda_id_to_products(new_cucarda_id)
          products.update_all(cucarda_id: new_cucarda_id)     
      end
      
    end

    def update_cucarda_id_for_categories(store_id = nil)
      return unless update_cucarda_id?
      new_cucarda_id = cucarda_active? ? cucarda_id : nil
      store_names = self.store_names
      
      categories = descendants.any? ? descendants : Mkp::Category.where(id: self.id)
      categories.update_all( cucarda_id: new_cucarda_id, cucarda_active: cucarda_active,store_names: store_names )
      shop_ids = Mkp::ShopStore.where(store_id: store_id).pluck(:shop_id) 
      categories_ids = categories.pluck(:id)  
      Mkp::Product.active.with_stock.where(shop_id: shop_ids, category_id: categories_ids).update_all(cucarda_id: new_cucarda_id)
    end

    def update_cucarda_id_for_shop
      return unless update_cucarda_id?

      new_cucarda_id = cucarda_active? ? cucarda_id : nil

      update_columns(cucarda_id: new_cucarda_id)
      update_cucarda_id_to_products(new_cucarda_id)
    end

    def check_cucarda_new_product
      self.cucarda_id = shop&.cucarda_id || category&.cucarda_id
    end

    def check_cucarda_update_product
      return unless attribute_changed?('cucarda_id') || attribute_changed?('cucarda_active')
      
      shop_cucarda_id = shop&.cucarda_id
      category_cucarda_id = category&.cucarda_id
      if cucarda_id.blank? && shop_cucarda_id.present? && category_cucarda_id.present?
        update_columns(cucarda_id: shop_cucarda_id)
      end
    end
  end
end
