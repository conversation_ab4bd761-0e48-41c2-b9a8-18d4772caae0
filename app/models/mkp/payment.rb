module Mkp
  class Payment < ActiveRecord::Base
    before_update :save_caller_if_canceled

    belongs_to :order
    belongs_to :order_item
    belongs_to :sale_item, polymorphic: true

    serialize :gateway_data, Hash

    STATUS = %w(pending collected cancelled refunded).freeze

    CANCELLABLE_STATUSES = %w(pending cancelled collected).freeze

    INSTALLMENTS_MAP = { 7 => 12, 8 => 18, 13 => 3, 16 => 6, 25 => 24, 31 => 30 }.freeze

    scope :expired, -> { where('created_at <= ?', 14.minutes.ago) }
    scope :pending, -> { where(status: 'pending') }
    scope :paid_with, ->(gateway_names) { where(gateway: gateway_names) }
    scope :with_points, -> { where(gateway: 'LoyaltyBna') }

    scope :paid_with_and_pending_expired, ->(gateway_names) {
      gateway_names = Array(gateway_names)
      paid_with(gateway_names)
      .pending
      .expired
    }

    STATUS.each do |status|
      define_method("#{status}?".to_sym) do
        attributes['status'] == status
      end
    end

    def self.options_for_select
      STATUS.map { |state| [state.titleize, state] }
    end

    def cancel!(store = nil)
      return unless actual_gateway.present?

      if CANCELLABLE_STATUSES.include?(status)
        response = store ? actual_gateway.cancel_payment!(gateway_object_id, store) : actual_gateway.cancel_payment!(gateway_object_id)
        update_information!(response)

        reload
      end
    end

    def cancel_by_store!(store)
      #solucion temporal
      return unless actual_gateway.present?

      if CANCELLABLE_STATUSES.include?(status)
        response = store ? actual_gateway.cancel_payment_by_store!(gateway_object_id, store) : actual_gateway.cancel_payment!(gateway_object_id)
        update_information!(response)

        reload
      end
    end

    def sub_payment_amount(suborder)
      unless decidir_distributed_gateway? || avenida_decidir_distributed_gateway? || avenida_modo_distributed_gateway? || loyalty_bna_gateway?
        return suborder.total.to_f 
      end

      amount = get_subpayment_site_id(suborder, suborder.shop)&.dig('amount') || 0
      if decidir_distributed_gateway? || avenida_decidir_distributed_gateway? || loyalty_bna_gateway?
        amount_to_decimal(amount.to_s)
      else
        if sub_payment_coef == 0
          amount.round(2)
        else
          (amount * sub_payment_coef).round(2)
        end
      end
    end

    def cancel_by_credentials!(credential)
      return unless actual_gateway.present?

      if CANCELLABLE_STATUSES.include?(status)
        response = actual_gateway.cancel_payment_by_credentials!(gateway_object_id, credential)
        update_information!(response)
        reload
      end
    end

    def calculate_installment_amount(suborder)
      if decidir_distributed_gateway? || avenida_decidir_distributed_gateway?
        (sub_payment_amount(suborder) / installments).to_f.truncate(2)
      elsif modo_gateway?
        gateway_data.dig(:response, :installments, :amount)
      else
        installments == 0 ? suborder.total.to_i : (suborder.total.to_i / installments)
      end
    end

    def sub_payment_ticket(suborder)
      if decidir_distributed_gateway? || avenida_decidir_distributed_gateway?
        get_subpayment_site_id(suborder, suborder.shop)&.dig('ticket')
      elsif modo_gateway?
        ticket = sub_payment_for_suborder(suborder)[:ticket] if sub_payment_for_suborder(suborder)
      else
        suborder.payment.gateway_data[:processor_receipt_number]
      end
    end

    def get_fiserv_ticket(gateway_object_id)
      find_any_payment(gateway_object_id).gateway_data[:processor_receipt_number]
    end

    def get_fiserv_cod_auth(gateway_object_id)
      find_any_payment(gateway_object_id).gateway_data[:processor_approval_code]
    end

    def find_any_payment(gateway_object_id)
      Mkp::Payment.find_by(gateway_object_id: gateway_object_id)
    end

    def sub_payment_code_authorization(suborder)
      if decidir_distributed_gateway? || avenida_decidir_distributed_gateway?
        get_subpayment_site_id(suborder, suborder.shop)&.dig('card_authorization_code')
      elsif modo_gateway?
        card_authorization_code = sub_payment_for_suborder(suborder)[:card_authorization_code] if sub_payment_for_suborder(suborder)
      else
        suborder.payment.gateway_data[:processor_approval_code]
      end
    end

    # implementar metodo en Avenida::Payments
    def amount_to_decimal(string_amount)
      (string_amount.to_f / 100)
    end

    # implementar metodo en Avenida::Payments
    def get_subpayment_site_id(suborder, shop)
      return unless eligible_gateway?
    
      site_id = shop.get_decidir_site_id(suborder.store)
      data = subpayment_data_for_gateway
    
      return nil unless data.present?
    
      result = data.detect { |p| p['site_id'] == site_id }
      return result if result.present?
    
      if modo_gateway?
        result = data.detect { |p| p['site_id']&.chomp(p['site_id']&.last) == site_id }
        return result if result.present?
      end
    
      nil
    end

    def sub_payment_coef
      begin
        return 0.0 if gateway_data&.dig(:installment_data, :coef).nil? && gateway_data&.dig(:response, :amount).nil?

        if decidir_distributed_gateway? || avenida_decidir_distributed_gateway?
          gateway_data.dig(:installment_data, :coef).to_f
        elsif modo_gateway?
          gateway_data['response']['installments']['cft'].nil? && gateway_data.dig(:response, :installments, :quantity) == [0, 1] ?
            (gateway_data.dig(:response, :amount).to_f / gateway_data.dig(:total_amount).to_f).round(8) :
            (gateway_data.dig(:total_amount).to_f / gateway_data.dig(:request, :transactionAmount).to_f).round(8)
        else
          gateway_data.dig(:installment_data, :coef).to_f
        end
      rescue
        0.0
      end
    end

    def amount_coef(suborder)
      return 0.0 if sub_payment_coef.zero?

      amount = sub_payment_amount(suborder)
      (amount - (amount / sub_payment_coef)).to_f.round(2)
    end

    def refund!
      if status == "collected"
        response = refund_amount!(collected_amount)
        update_information!(response)

        reload
      end
    end

    def returned?
      cancelled? || refunded?
    end

    def refund_amount!(amount, aditional_data = nil)
      if status == "collected"
        response = actual_gateway.refund_partial!(gateway_object_id, amount, aditional_data)
      end
    end

    def refund_partial_and_update_payment!(suborder, aditional_data = nil)
      if status == "collected"
        actual_gateway.refund_partial_and_update_payment!(self, suborder, aditional_data)
      end
    end

    def refresh
      refresh_information(true)
    end

    def refresh!
      refresh_information(false)
    end

    def get_gateway_status
      return unless actual_gateway.present?
      actual_gateway.get_status(gateway_object_id)
    end

    def get_error
      return {} unless cancelled?
      actual_gateway.get_error(gateway_data)
    end

    def get_history
      return [] unless actual_gateway.present?
      actual_gateway.get_history(gateway_data)
    end

    def get_customer_legal_id
      return document_number if document_number.present?
      return unless actual_gateway.present?
      actual_gateway.get_customer_legal_id(gateway_data)
    end

    def gateway_payment_url
      return unless actual_gateway.present? && actual_gateway.lookup_url.present?
      "#{actual_gateway.lookup_url}#{gateway_object_id}"
    end

    def get_cc_brand
      begin
        return "-" if payment_method == "ticket"
        actual_gateway.get_cc_brand(gateway_object_id) || gateway_data.dig(:response, :card, :issuer_name)
      rescue
        '-'
      end
    end

    def get_installments
      return "-" if payment_method == "ticket"
      begin
        actual_gateway&.get_installments(gateway_object_id) || gateway_data&.dig(:response, :installments, :quantity) || '1'
      rescue
        '1'
      end
    end

    def get_cc_bin
      return "-" if payment_method == "ticket"
      actual_gateway&.get_cc_bin(gateway_object_id) || '-'
    end

    def get_cc_bank
      begin
        return "-" if !(payment_method != "ticket") || !actual_gateway.respond_to?(:get_cc_bank)
        actual_gateway.get_cc_bank(gateway_object_id)
      rescue
        '-'
      end
    end

    def get_brand_method
      actual_gateway.try(:get_brand_method, gateway_object_id)
    end

    def financial_cost_value
      fin_cost_value = actual_gateway.try(:financial_cost_value, gateway_object_id)

      if fin_cost_value.present? && fin_cost_value.to_f.positive?
        fin_cost_value.to_f.round(2)
      else
        if gateway_data[:installment_data].present? && gateway_data[:installment_data][:coef].present?
          coef = (gateway_data[:installment_data][:coef]).to_f

          return 0 if coef.zero?

          fin_cost_value = (collected_amount - (collected_amount / coef)).to_f
          fin_cost_value.round(2)
        else
          0
        end
      end
    end

    def get_credit_or_debit
      actual_gateway.try(:get_credit_or_debit, gateway_object_id)
    end

    def get_external_id(aditional_data=nil)
      actual_gateway.external_id(gateway_object_id, aditional_data)
    end

    def apply_bonified?
      gateway_data["installments"] == 1 && gateway == "Decidir"
    end

    def payment_method_name
      if payment_method == 'visa_puntos'
        'Puntos'
      elsif gateway == 'DecidirDistributedMacro' || gateway == 'Decidir'
        'Pesos'
      else
        payment_method.present? ? payment_method.titleize : gateway.try(:titleize)
      end
    end
    # Este metodo tiene como fin indicar en que moneda o unidad se encuentra la transaccion
    def unit
      return 'Puntos' if ["LoyaltyBna", "VisaPuntos"].include?(gateway)
      'Pesos'
    end
    # Devuelve la cantidad de puntos usados para la transaccion
    def points_used
      if unit == 'Puntos' && gateway_data.kind_of?(Object) && gateway_data.key?("points")
        gateway_data["points"]      
      else
        0
      end 
    end

    def spanish_status
      spanish = {
          'pending' => 'Pendiente',
          'collected' => 'Cobrado',
          'cancelled' => 'Cancelado',
          'refunded' => 'Reintegrado'
      }
      spanish[status]
    end

    def government_installments
      INSTALLMENTS_MAP[self.installments]
    end

    def number_of_installments(payment)
      return if payment.installments.blank?
      return "Plan ahora #{payment.government_installments}" if payment.government_installments.present?

      payment.installments
    end

    def last_gateway_data
      return gateway_data unless actual_gateway.try(:qr_type?)

      get_last_notification_payload || gateway_data
    end

    def sub_gateway_object_id
      actual_gateway.try(:sub_gateway_object_id, last_gateway_data)
    end

    def is_decidir?
      decidir_distributed_gateway? || avenida_decidir_distributed_gateway? || modo_gateway?
    end

    def number_of_installments_without_text
      return if self.installments.blank?
      return self.government_installments if self.government_installments.present?

      self.installments
    end

    def card_last_four_digits
      begin
        last_digits = gateway_data.dig(:payer, :identification, :card_last_four_digits) || gateway_data.dig(:response, :card, :last_digits)
        last_digits || actual_gateway.try(:card_last_four_digits, last_gateway_data)
      rescue
        '-'
      end
    end

    def card_holder_name
      begin
        holder = gateway_data.dig(:payer, :identification, :name_payment)
        full_name = "#{gateway_data.dig(:response, :payer, :last_name)} #{gateway_data.dig(:response, :payer, :first_name)}"
        holder || full_name
      rescue
        'unknown'
      end
    end

    def payer_dni
      begin
        dni = gateway_data.dig(:payer, :identification, :number)
        dni || gateway_data.dig(:response, :payer, :dni)
      rescue
        'unknown'
      end
    end

    def modo_gateway?
      ["AvenidaModoDistributed", "AvenidaModo"].include? gateway
    end

    def avenida_modo_distributed_gateway?
      ["AvenidaModoDistributed"].include? gateway
    end

    def sub_payment_for_suborder(suborder)
      # return false if suborder.payment.gateway_data.try(:response, :sub_payments).nil?

      sub_payments = gateway_data.dig(:response, :sub_payments)
      return false if sub_payments.nil?

      sub_payments.find { |sp| sp[:site_id] == suborder.shop.decidir_site_id }
    end

    def loyalty_bna_gateway?
      gateway == "LoyaltyBna"
    end

    def suborder_status(suborder)
      return 'refunded' if suborder.present? && suborder.refunded?
      status
    end

    private

    def eligible_gateway?
      decidir_distributed_gateway? || avenida_decidir_distributed_gateway? || modo_gateway? || loyalty_bna_gateway?
    end

    def current_gateway_key
      return :decidir if decidir_distributed_gateway?
      return :avenida_decidir if avenida_decidir_distributed_gateway?
      return :modo if modo_gateway?
      return :loyalty_bna if loyalty_bna_gateway?
    
      :unknown
    end

    def subpayment_data_for_gateway
      dispatch = {
        decidir: -> { gateway_data['sub_payments'] },
        avenida_decidir: -> { gateway_data['gateway']['sub_payments'] },
        loyalty_bna: -> { gateway_data['sub_payments'] },
        modo: -> {
          gateway_data&.dig('original_payload', 'gateway', 'sub_payments') ||
          gateway_data&.dig('gateway', 'sub_payments') ||
          gateway_data['sub_payments']
        }
      }
    
      dispatch[current_gateway_key]&.call
    end

    def actual_gateway
      @actual_gateway ||= "Avenida::Payments::#{gateway}".constantize
    rescue NameError => e
      # Ignored
    end

    def decidir_distributed_gateway?
      gateway == "DecidirDistributed"
    end

    def refresh_information(optional = true)
      return unless actual_gateway.present?

      response = actual_gateway.get_payment(gateway_object_id)
      response = response.with_indifferent_access
      if !optional || status_changed?(actual_gateway::STATUSES[response[:status]])
        update_information!(response)
      end

      reload
    end

    def get_last_notification_payload
      return nil if gateway_data[:notifications_payloads].blank?

      key = gateway_data[:notifications_payloads].keys.max
      gateway_data.dig(:notifications_payloads, key)
    end

    def avenida_decidir_distributed_gateway?
      gateway == "AvenidaDecidirDistributed"
    end

    def update_information!(payload)
      return unless payload.present? && payload[:id].present?

      new_status = actual_gateway::STATUSES[payload[:status]]
      status_changed = status_changed?(new_status)
      collected_for_first_time = collected_for_first_time?(new_status) # It's important to not
                                                                       # move this around, because
                                                                       # there are consecuences
      data = {
        status: new_status,
        gateway_data: update_gateway_data_information(payload),
        cancelation_reason: new_status == 'cancelled' || 'refunded' ? caller : nil
      }.tap do |data|
        data[:collected_at] = payload[:date_approved] if collected_for_first_time
        if payload.dig('response', 'amount').present?
          data[:collected_amount] = payload['response']['amount']
        end
        if payload.dig('response', 'installments', 'quantity').present?
          data[:installments] = payload['response']['installments']['quantity']
        end
        if ["AvenidaModo", "AvenidaModoDistributed"].include?(gateway)
          unless payload.dig(:response, :payer, :dni).nil?
            data[:document_number] = payload.dig(:response, :payer, :dni)
            data[:document_type] = "DNI" 
          end
        end
      end

      update_attributes(data)

      # NOTE: This probably should not be here, let's discuss if there is a better place
      # maybe when we create that OrderProcessor or something similar.
      #
      # This is particulary DANGEROUS because of the idea of having multiple payments associated
      # to an order, so doing as follow will modify the order without having in mind the precense
      # of other payments, right now we are not covering multiple payments on an order but, still.
      if status_changed && collected_for_first_time
        order.generate_brukman_order
        resume_paid_order_processing if order.store.production?
      end
    end

    def status_changed?(new_status)
      status != new_status
    end

    def collected_for_first_time?(new_status)
      new_status == 'collected' && collected_at.blank?
    end

    def update_gateway_data_information(new_payload)
      new_payload.deep_dup.tap do |data|
        data[:original_payload] = gateway_data[:original_payload] || gateway_data
        data[:notifications_payloads] = gateway_data[:notifications_payloads] || {}
        data[:notifications_payloads][Time.now.to_s] = new_payload
      end
    end

    def resume_paid_order_processing
      order.items.each do |order_item|
        order_item.variant.decrease_quantity(order_item.quantity)
        order_item.variant.decrease_reserved_quantity(order_item.quantity)
      end

      order.reload.shipments.each do |shipment|
        if Network[order.network].shops_can_be_fulfilled? &&
          order.is_paid? &&
          shipment.fulfilled_by_gp?

          ::Mkp::Shipments::ShipnowOrderProcessWorker.perform_async(shipment.id)
        end
      end

      %w(customer sellers network_admins).each do |receiver|
        ::Mkp::PurchaseProcessor.send(:"notify_paid_to_#{receiver}", order)
      end

      order.reload.solr_index

      ::Mkp::PurchaseProcessor.send(:notify_affiliate_networks, order)
      ::Mkp::PurchaseProcessor.send(:notify_purchase_to_webhooks, order)
    end

    def save_caller_if_canceled
      if status == 'cancelled' && status_was != 'cancelled' && cancelation_reason.blank?
        update_attributes(cancelation_reason: caller)
      end
    end
  end
end
