max_installments = product.category.max_installments(@current_store, product)
max_installments_info = product.max_installments_info(@current_store, product)

json.extract! product, :id, :title, :description, :available_properties, :available_properties_names, :currency_symbol, :currency_code, :slug,
  :purchasable, :reservable, :transaction_type, :points_price
if product.cucarda_id.present?
  cucarda = Cucarda.find(product.cucarda_id)
  json.cucarda do
    json.extract! cucarda, :id, :descripcion, :image
  end
end
json.max_installments max_installments
json.max_installments_info max_installments_info
json.available_installments product.available_installments(@current_store)
json.available_properties product.available_properties.map { |p| p.is_a?(Hash) ? p[:slug] : p }

json.category do
  json.partial! 'api/angular_app/v1/categories/category', category: product.category
end

product_data = product.data_to_simple_hash.map{ |key, value| { key: key, value: value } }
product_data.push(
  { key: 'Origen del Producto', value: product.translated_values_origin_of_product },
  { key: 'Marca', value: product.brand }
)
json.data product_data

json.data_shipment product.data_to_simple_hash(:data_shipment).map{ |key, value| {key: key,  value: value} }
json.breadcrumb Mkp::Breadcrumb.new(product).adapt

json.total_stock product.total_stock_cached
json.unavailable product.unavailable?
json.pickeable product.pickeable?
json.has_no_property product.has_no_property?
json.is_voucher product.is_voucher?
json.regular_price "%.2f" % product.regular_price
json.regular_price_without_taxes product.regular_price_without_taxes && "%.2f" % product.regular_price_without_taxes

sale_calculator = Mkp::ProductSaleDetector.new(product)
json.is_on_sale sale_calculator.is_on_sale?
if sale_calculator.is_on_sale?
  json.sale_price sale_calculator.calculate
  json.percent_off sale_calculator.percent_off
  json.sale_price_without_taxes sale_calculator.calculate_without_taxes
end

json.propertiable product.available_properties do |property|
  values = product.available_values_for(property)
  if property.is_a? Hash
    json.key property[:slug]
    json.data values_by_slug
    json.name property[:name]
  elsif property.to_s == 'size' && product.all_integers?(values)
    json.key property
    json.data values.sort
  else
    json.key property
    json.data values
  end
end

json.pictures product.pictures do |picture|
  json.id picture.id
  json.thumb picture.url(:t)
  json.large picture.url(:l)
  json.st picture.url(:st)
end

json.manufacturer do
  json.partial! 'api/angular_app/v1/manufacturers/manufacturer', manufacturer: product.manufacturer
end

json.shop do
  json.partial! 'api/angular_app/v1/shops/shop', shop: product.shop
end

json.variants product.variants do |variant|
  json.partial! 'api/angular_app/v1/variants/variant', variant: variant
end

json.promotions Promotions::Serializer.new(product, product.price, max_installments, @current_store).build do |promotion|
  json.partial! 'api/angular_app/v1/promotions/promotion', promotion: promotion
end
